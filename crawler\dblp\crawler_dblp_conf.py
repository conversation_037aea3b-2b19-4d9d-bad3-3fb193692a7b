import os
import sys
from tokenize import Special

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin
from selenium.webdriver.common.by import By
from crawler.dblp.crawler_dblp import DBLPCrawler
import logging

DBLP_BASE_URL = 'https://dblp.uni-trier.de/db/conf' # DBLP 基础 URL

def get_special_tail(source: str, target_year: str):
    if source == 'nips' and int(target_year) >= 2020:
        return f'nips/neurips{target_year}'
    if source == 'sigmod':
        return f'sigmod/sigmod{target_year}c'
    else:
        return f'{source}/{source}{target_year}'

class DBLPCrawlerConf(DBLPCrawler):
    """DBLP 爬虫基类"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        # 构建DBLP URL（包括特殊处理）
        source_tail = get_special_tail(target_source, target_year)
        self.base_url = f"{DBLP_BASE_URL}/{source_tail}.html" # DBLP 基础 URL

        self.no_text = [
            'proceedings',
            'fullHtml',
            'full'
        ]

    def _get_paper_links(self) -> List[str]:
        """获取论文链接"""
        return self._get_paper_links_from_current_page(self.base_url)

if __name__ == '__main__':
    crawler = DBLPCrawler(target_year='2024', target_source='aaai')
    output = crawler.get_paper_list(max_papers=10000)
    print(output)
import os
import sys

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin
from selenium.webdriver.common.by import By
from crawler.base import BaseCrawler
import logging

ACM_BASE_URL = 'https://dl.acm.org' # ACM 基础 URL

class ACMCrawler(BaseCrawler):
    """ACM 爬虫基类"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        year = str(target_year)
        year_start = f"{year[:3]}0"

        self.base_url = f"https://dl.acm.org/conference/{target_source.lower()}/group/d{year_start}.y{year}"
        self.no_text = [
            'proceedings',
            'fullHtml',
            'full'
        ]

    def _get_papers_from_current_page(self) -> List[str]:
        """从当前页面获取论文链接"""
        # 提供摘要页面（注意带有 abs/ 是摘要，但是去掉就是全文）
        paper_links = []
        seen_dois = set()  # 用于去重，避免同一篇论文的不同URL被重复添加

        # 查找论文链接的选择器
        paper_selectors = [
            'a[href*="/doi/"]',  # DOI链接
            '.issue-item__title a',
            '.hlFld-Title a',
            '.citation__title a',
            'h5 a[href*="/doi/"]',
            '.article-title a',
            '.title a[href*="/doi/"]'
        ]

        for selector in paper_selectors:
            elements = self.driver_manager.driver.find_elements(By.CSS_SELECTOR, selector)
            logging.debug(f"选择器 '{selector}' 找到 {len(elements)} 个元素")

            for element in elements:
                href = element.get_attribute('href')
                if any(text in href for text in self.no_text):
                    continue
                if href and '/doi/' in href:
                    # 确保链接指向论文详情页面而不是PDF
                    if '/pdf/' in href or '/epdf/' in href:
                        # 将PDF链接转换为论文详情页面链接
                        href = href.replace('/pdf/', '/').replace('/epdf/', '/')

                    doi_match = href.split('/doi/')[-1]
                    if doi_match.startswith('abs/'):
                        doi_match = doi_match[4:]  # 去掉 'abs/' 前缀

                    full_url = urljoin(ACM_BASE_URL, f"doi/abs/{doi_match}")

                    # 基于DOI去重
                    if doi_match not in seen_dois:
                        seen_dois.add(doi_match)
                        paper_links.append(full_url)

        logging.debug(f"从当前页面找到 {len(paper_links)} 篇论文（去重后）")
        return paper_links

    def _crawl_paper_details(self, paper_url: str) -> Optional[Dict]:
        """爬取单篇TKDD论文的详细信息 - TKDD特异性数据提取"""
        try:
            if not self.driver_manager.safe_get(paper_url):
                logging.error(f"无法访问论文页面: {paper_url}")
                return None

            paper_data = {
                'url': paper_url,
                'crawl_time': datetime.now().isoformat()
            }

            # 提取标题
            title_selectors = ['h1.citation__title', 'h1.hlFld-Title', '.citation__title', 'h1']
            for selector in title_selectors:
                title = self.driver_manager.extract_text_safe(selector)
                if title:
                    paper_data['title'] = title
                    break

            # 提取作者
            author_selector = 'span[property="author"]'
            authors = self.driver_manager.extract_text_safe(author_selector, multiple=True)
            paper_data['authors'] = authors

            # 提取摘要
            abs_selector = '#abstract'
            abstract = self.driver_manager.extract_text_safe(abs_selector)
            cleaned_abstract = self._clean_abstract_text(abstract)
            paper_data['abstract'] = cleaned_abstract
            logging.info(f"获取到摘要 ({len(cleaned_abstract)} 字符)")

            # 提取DOI - 从TKDD URL中提取
            import re
            doi_match = re.search(r'/doi/(.+)', paper_url)
            if doi_match:
                paper_data['doi'] = doi_match.group(1)

            # 提取发表时间
            pub_selector = '.core-date-published'
            pub_date = self.driver_manager.extract_text_safe(pub_selector)
            paper_data['publication_date'] = pub_date

            # 提取PDF链接
            pdf_selector = 'a[title*="View PDF"]'
            pdf_url = self.driver_manager.extract_attribute_safe(pdf_selector, 'href')
            paper_data['pdf_url'] = urljoin(ACM_BASE_URL, pdf_url)

            return paper_data if paper_data.get('title') else None

        except Exception as e:
            logging.error(f"爬取论文详情失败 ({paper_url}): {e}")
            return None

import os
import sys

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin
from selenium.webdriver.common.by import By
import logging

from crawler.acm.crawler_acm import ACMCrawler, ACM_BASE_URL

class ACMCrawlerLoi(ACMCrawler):
    """TKDD loi 论文爬虫 - 按卷/期浏览内容"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        year = str(target_year)
        year_start = f"{year[:3]}0"

        self.base_url = f"https://dl.acm.org/loi/{target_source.lower()}/group/d{year_start}.y{year}"

    def _get_paper_links(self) -> List[str]:
        """从TKDD归档页面获取论文链接列表"""

        logging.info(f"🔍 正在扫描 {self.target_year} 年的论文，URL: {self.base_url}")

        # 从指定年份页面获取论文链接
        if not self.driver_manager.safe_get(self.base_url):
            logging.error(f"无法访问年份页面: {self.base_url}")
            return []
        all_paper_links = self._get_all_papers_from_year()

        logging.info(f"✅ 从 {self.target_year} 年获取到 {len(all_paper_links)} 篇论文")

        return all_paper_links

    def _get_all_papers_from_year(self) -> List[str]:
        """获取年份页面的所有论文链接（不限制数量）"""
        all_paper_links = []

        # 首先尝试查找卷期链接，然后从每个卷期获取论文
        volume_links = self._get_volume_links_from_year()

        if volume_links:
            logging.info(f"找到 {len(volume_links)} 个卷期")
            for volume_title, volume_url in volume_links:
                logging.info(f"正在扫描卷期: {volume_title}")
                volume_papers = self._get_papers_from_volume(volume_url)  # 不限制数量
                all_paper_links.extend(volume_papers)
                logging.info(f"   从该卷期获取到 {len(volume_papers)} 篇论文")
        else:
            # 如果没有找到卷期链接，直接在当前页面查找论文链接
            logging.info("未找到卷期链接，直接在年份页面查找论文")
            all_paper_links = self._get_papers_from_current_page()  # 不限制数量

        return all_paper_links

    def _get_volume_links_from_year(self) -> List[tuple]:
        """从年份页面获取卷期链接"""
        volume_links = []

        try:
            # 查找卷期链接的选择器
            volume_selectors = [
                'a[href*="/toc/"]',  # 卷期目录链接
                '.issue-item a',
                '.volume-link a',
                'a[href*="issue"]',
                '.toc-link a'
            ]

            for selector in volume_selectors:
                elements = self.driver_manager.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    href = element.get_attribute('href')
                    text = element.text.strip()

                    if href and text:
                        # 跳过非年份特定的卷期，如"Just Accepted"
                        if any(skip_text in text.lower() for skip_text in ['just accepted', 'latest', 'current', 'forthcoming']):
                            logging.info(f"跳过非年份特定的卷期: {text}")
                            continue

                        # 跳过包含justaccepted的URL
                        if 'justaccepted' in href.lower():
                            logging.info(f"跳过Just Accepted URL: {href}")
                            continue

                        full_url = urljoin(ACM_BASE_URL, href)
                        if (text, full_url) not in volume_links:
                            volume_links.append((text, full_url))
                            logging.debug(f"找到卷期: {text} -> {full_url}")

                if volume_links:  # 如果找到了卷期链接，就不再尝试其他选择器
                    break

        except Exception as e:
            logging.error(f"获取卷期链接失败: {e}")

        return volume_links

    def _get_papers_from_volume(self, volume_url: str) -> List[str]:
        """从卷期页面获取论文链接"""
        paper_links = []

        if not self.driver_manager.safe_get(volume_url):
            logging.error(f"无法访问卷期页面: {volume_url}")
            return paper_links

        return self._get_papers_from_current_page()

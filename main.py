"""
学术论文爬虫 - 统一调用框架
支持多个出版商的期刊和会议论文爬取
"""

import logging
import argparse
import os
from datetime import datetime

from config import PUBLICATIONS

def setup_logging(source: str, year: str):
    """设置日志配置"""
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 生成日志文件名：source_year_YYYYMMDD_HHMMSS.log
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f"{source}_{year}_{current_time}.log"
    log_path = os.path.join(log_dir, log_filename)

    # 配置日志格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # 配置日志处理器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),  # 文件输出
            logging.StreamHandler()  # 控制台输出
        ]
    )

    logging.info(f"日志文件已创建: {log_path}")
    return log_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='学术论文爬虫')
    parser.add_argument('--source', type=str,  default='sigmod', help='期刊/会议标识 (如: tkdd, tochi, group, icml)')
    parser.add_argument('--max-papers', type=int, default=5, help='最大爬取论文数量')
    parser.add_argument('--year', type=str, default='2024', help='目标年份')
    parser.add_argument('--just-get-links', type=bool, default=True, help='是否只获取论文链接')
    parser.add_argument('--just-get-paper-data', type=bool, default=False, help='是否只获取论文数据')

    args = parser.parse_args()

    # 设置日志
    log_path = setup_logging(args.source, args.year)
    print(f"🎯目标年份: {args.year or '所有年份'}")
    print(f"🎯最大论文数: {args.max_papers}")
    print(f"📝日志文件: {log_path}")

    try:
        crawler = PUBLICATIONS[args.source](args.year, args.source)
        crawler.start_crawling(max_papers=args.max_papers, 
                               just_get_links=args.just_get_links, 
                               just_get_paper_data=args.just_get_paper_data)
        stats = crawler.get_stats()
        print(f"\n📊 爬取完成: {stats['total_papers']} 篇论文")

    except Exception as e:
        print(f"❌ 爬取失败: {e}")

if __name__ == "__main__":
    main()

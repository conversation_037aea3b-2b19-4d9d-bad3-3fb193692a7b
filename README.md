# 学术论文爬虫 - 统一调用框架

支持多个出版商的期刊和会议论文爬取，包括 ACM、IEEE、Springer、Elsevier 等。

## 功能特点

- **多出版商支持**: 统一接口调用不同出版商的爬虫
- **期刊和会议**: 支持期刊论文和会议论文爬取
- **年份过滤**: 支持按指定年份爬取论文
- **统一输出**: 所有爬虫输出统一的 JSON 和 CSV 格式
- **简化调用**: 通过期刊/会议标识符一键调用

## 支持的期刊和会议

### ACM 出版商
- `tkdd`: ACM Transactions on Knowledge Discovery from Data (期刊)
- `kdd`: ACM SIGKDD Conference on Knowledge Discovery and Data Mining (会议)

### IEEE 出版商  
- `tkde`: IEEE Transactions on Knowledge and Data Engineering (期刊)
- `icdm`: IEEE International Conference on Data Mining (会议)

### Springer 出版商
- `dmkd`: Data Mining and Knowledge Discovery (期刊)

### Elsevier 出版商
- `is`: Information Sciences (期刊)

## 使用方法

### 查看支持的期刊和会议
```bash
python main.py --list
```

### 爬取指定期刊/会议
```bash
# 爬取 TKDD 期刊 2023 年的论文，最多 10 篇
python main.py tkdd --year 2023 --max-papers 10

# 爬取 KDD 会议 2022 年的论文
python main.py kdd --year 2022 --max-papers 20

# 爬取 IEEE TKDE 期刊所有年份的论文
python main.py tkde --max-papers 50
```

### 命令行参数
- `publication`: 期刊/会议标识符 (必需)
- `--year`: 目标年份 (可选，默认爬取所有年份)
- `--max-papers`: 最大爬取论文数量 (可选，默认 10)
- `--list`: 列出所有支持的期刊和会议
- `--publisher`: 按出版商过滤 (可选，暂未实现)

## 输出文件

爬取的数据会保存在 `output/` 目录下，文件命名格式：
- JSON: `{期刊标识}_{年份}.json`
- CSV: `{期刊标识}_{年份}.csv`

例如：
- `tkdd_2023.json`
- `tkdd_2023.csv`

## 架构设计

### 基类设计
所有爬虫继承自 `BaseCrawler` 基类，提供统一的接口：
- `start_crawling()`: 开始爬取
- `_get_paper_links()`: 获取论文链接
- `_crawl_paper_details()`: 爬取论文详情
- `get_stats()`: 获取统计信息

### 扩展新期刊/会议
1. 在对应出版商的爬虫类中实现具体逻辑
2. 在 `main.py` 的 `PUBLICATIONS` 配置中添加新条目
3. 更新 `PUBLISHERS` 映射关系

## 环境要求

- Python 3.8+
- Selenium WebDriver
- Chrome 浏览器
- 相关 Python 依赖包

## 注意事项

- 目前只有 ACM TKDD 爬虫完全实现，其他爬虫为框架占位
- 支持代理访问，配置在 `.env.local` 文件中
- 遵循网站的 robots.txt 和访问频率限制

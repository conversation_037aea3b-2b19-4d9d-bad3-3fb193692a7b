"""
Clash 代理自动切换器
每隔1分钟自动切换到指定地区的代理节点，并验证延迟
"""

import time
import json
import random
import logging
import requests
import threading
from typing import List, Dict, Optional


# 如果没有配置文件，使用默认值
CLASH_API_URL = "http://127.0.0.1:9090"
CLASH_SECRET = None
SWITCH_INTERVAL = 60
MAX_DELAY = 1000
PROXY_GROUP = "GLOBAL"
TARGET_REGIONS = [
    '日本', '韩国', '美国', '台湾', '新加坡',
    '香港', '法国', '澳大利亚', '爱尔兰', '加拿大', '印度',
    'Japan', 'Korea', 'US', 'USA', 'Taiwan', 'Singapore',
    'HongKong', 'HK', 'France', 'Australia', 'Ireland', 'Canada', 'India'
]
DELAY_TEST_URL = "http://www.gstatic.com/generate_204"


class ProxySwitcher:
    """Clash代理自动切换器"""
    
    def __init__(self,
                 clash_api_url: str = None,
                 secret: str = None,
                 switch_interval: int = None,
                 max_delay: int = None,
                 proxy_group: str = None):
        """
        初始化代理切换器

        Args:
            clash_api_url: Clash API地址
            secret: API密钥（如果有）
            switch_interval: 切换间隔（秒）
            max_delay: 最大允许延迟（毫秒）
            proxy_group: 要控制的代理组名称
        """
        # 使用配置文件的默认值
        self.clash_api_url = (clash_api_url or CLASH_API_URL).rstrip('/')
        self.secret = secret or CLASH_SECRET
        self.switch_interval = switch_interval or SWITCH_INTERVAL
        self.max_delay = max_delay or MAX_DELAY
        self.proxy_group = proxy_group or PROXY_GROUP
        self.running = False

        # 目标地区关键词
        self.target_regions = TARGET_REGIONS
        
        # 设置请求头
        self.headers = {'Content-Type': 'application/json'}
        if self.secret:
            self.headers['Authorization'] = f'Bearer {self.secret}'
            
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, data: dict = None) -> tuple[Optional[dict], int]:
        """发送API请求，返回(响应数据, 状态码)"""
        url = f"{self.clash_api_url}{endpoint}"
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, timeout=10)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data, timeout=10)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            result = response.json() if response.content else {}
            return result, response.status_code

        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败 {method} {endpoint}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                return None, e.response.status_code
            return None, 0
    
    def get_all_proxies(self) -> Optional[Dict]:
        """获取所有代理信息"""
        result, status_code = self._make_request('GET', '/proxies')
        return result
    
    def get_proxy_delay(self, proxy_name: str, test_url: str = None) -> Optional[int]:
        """测试代理延迟"""
        if test_url is None:
            test_url = DELAY_TEST_URL
        endpoint = f"/proxies/{proxy_name}/delay"
        params = f"?timeout=5000&url={test_url}"

        result, status_code = self._make_request('GET', endpoint + params)
        if result and 'delay' in result:
            return result['delay']
        return None
    
    def switch_proxy(self, proxy_name: str) -> bool:
        """切换到指定代理"""
        data = {"name": proxy_name}
        result, status_code = self._make_request('PUT', f'/proxies/{self.proxy_group}', data)

        # 根据Clash API文档，成功切换返回204状态码
        if status_code == 204:
            self.logger.info(f"✅ 代理切换成功: {proxy_name} (状态码: {status_code})")
            return True
        elif status_code == 400:
            self.logger.error(f"❌ 代理切换失败: 请求格式错误或代理类型错误 (状态码: {status_code})")
        elif status_code == 404:
            self.logger.error(f"❌ 代理切换失败: 代理不存在 '{proxy_name}' (状态码: {status_code})")
        else:
            self.logger.error(f"❌ 代理切换失败: 未知错误 (状态码: {status_code})")

        return False

    def verify_current_proxy(self) -> Optional[str]:
        """验证当前使用的代理"""
        all_proxies = self.get_all_proxies()
        if all_proxies and 'proxies' in all_proxies:
            proxy_group_info = all_proxies['proxies'].get(self.proxy_group)
            if proxy_group_info:
                return proxy_group_info.get('now')
        return None
    
    def get_target_proxies(self) -> List[str]:
        """获取目标地区的代理列表"""
        all_proxies = self.get_all_proxies()
        if not all_proxies or 'proxies' not in all_proxies:
            self.logger.error("无法获取代理列表")
            return []
        
        # 查找指定的代理组
        proxy_group_info = all_proxies['proxies'].get(self.proxy_group)
        if not proxy_group_info or 'all' not in proxy_group_info:
            self.logger.error(f"找不到代理组: {self.proxy_group}")
            return []
        
        available_proxies = proxy_group_info['all']
        target_proxies = []
        
        # 筛选包含目标地区关键词的代理
        for proxy in available_proxies:
            for region in self.target_regions:
                if region.lower() in proxy.lower():
                    target_proxies.append(proxy)
                    break
        
        self.logger.info(f"找到 {len(target_proxies)} 个目标地区代理")
        return target_proxies
    
    def find_best_proxy(self, proxies: List[str]) -> Optional[str]:
        """从代理列表中找到延迟最低且符合要求的代理"""
        valid_proxies = []
        
        for proxy in proxies:
            delay = self.get_proxy_delay(proxy)
            if delay is not None and delay <= self.max_delay:
                valid_proxies.append((proxy, delay))
                self.logger.info(f"代理 {proxy} 延迟: {delay}ms ✓")
            else:
                self.logger.warning(f"代理 {proxy} 延迟过高或无法连接: {delay}ms ✗")
        
        if not valid_proxies:
            return None
        
        # 按延迟排序，选择最快的
        valid_proxies.sort(key=lambda x: x[1])
        return valid_proxies[0][0]
    
    def switch_to_random_proxy(self):
        """切换到随机的目标地区代理"""
        target_proxies = self.get_target_proxies()
        if not target_proxies:
            self.logger.warning("没有找到目标地区的代理")
            return
        
        # 随机打乱代理列表
        random.shuffle(target_proxies)
        
        # 尝试找到最佳代理
        best_proxy = self.find_best_proxy(target_proxies[:5])  # 只测试前5个以节省时间
        
        if best_proxy:
            if self.switch_proxy(best_proxy):
                # 验证切换是否真的成功
                current_proxy = self.verify_current_proxy()
                if current_proxy == best_proxy:
                    self.logger.info(f"✅ 代理切换成功并验证: {best_proxy}")
                else:
                    self.logger.warning(f"⚠️ 代理切换API返回成功，但验证失败: 期望={best_proxy}, 实际={current_proxy}")
            else:
                self.logger.error(f"❌ 切换代理失败: {best_proxy}")
        else:
            self.logger.warning("⚠️ 没有找到符合延迟要求的代理")
    
    def start(self):
        """启动代理切换器"""
        self.running = True
        self.logger.info(f"🚀 代理切换器启动 - 间隔: {self.switch_interval}秒, 最大延迟: {self.max_delay}ms")
        
        # 立即执行一次切换
        self.switch_to_random_proxy()
        
        while self.running:
            time.sleep(self.switch_interval)
            if self.running:  # 再次检查，防止在sleep期间被停止
                self.switch_to_random_proxy()
    
    def stop(self):
        """停止代理切换器"""
        self.running = False
        self.logger.info("🛑 代理切换器已停止")


def main():
    """独立运行代理切换器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(levelname)s - %(message)s'
    )
    
    switcher = ProxySwitcher()
    
    try:
        switcher.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        switcher.stop()


if __name__ == "__main__":
    main()

import os
import sys

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin
from selenium.webdriver.common.by import By
import logging

from crawler.acm.crawler_acm import ACMCrawler, ACM_BASE_URL

class ACMCrawlerConf(ACMCrawler):
    """TKDD conference 论文爬虫 - 会议分页浏览内容"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        year = str(target_year)

        # 通过 startPage=n 来控制多页，直到某一页没有任何内容
        url_start = "https://dl.acm.org/topic/conference-collections/"
        url_tail = f"{target_source.lower()}?pageSize=50&AfterYear={year}&BeforeYear={year}"

        self.base_url = url_start + url_tail

    def _get_paper_links(self) -> List[str]:
        """从TKDD归档页面获取论文链接列表"""

        logging.info(f"🔍 正在扫描 {self.target_year} 年的论文，URL: {self.base_url}")

        # 1. 无限循环增加页数获取论文链接，直到没有内容
        all_paper_links = []
        page = 0
        while True:
            logging.info(f"正在扫描第 {page} 页...")
            self.driver_manager.driver.get(self.base_url + f"&startPage={page}")
            # 检查是否有新的论文链接
            new_links = self._get_papers_from_current_page()  # 不限制数量
            if not new_links:
                logging.info("未找到更多论文链接，跳出循环")
                break
            all_paper_links.extend(new_links)
            logging.info(f"   已获取到 {len(new_links)} 篇新论文")
            page += 1

        logging.info(f"✅ 从 {self.target_year} 年获取到 {len(all_paper_links)} 篇论文")

        return all_paper_links

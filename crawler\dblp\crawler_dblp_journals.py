import os
import sys
from tokenize import Special

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from selenium.webdriver.common.by import By
from crawler.dblp.crawler_dblp import DBLPCrawler

import logging

DBLP_BASE_URL = 'https://dblp.uni-trier.de/db/journals' # DBLP 基础 URL

class DBLPCrawlerJournals(DBLPCrawler):
    """DBLP 爬虫基类"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        # 构建DBLP URL（期刊在这个页面包含所有卷以及对应的年份）
        self.base_url = f"{DBLP_BASE_URL}/{target_source.lower()}/index.html" # DBLP 基础 URL

        self.no_text = [
            'proceedings',
            'fullHtml',
            'full'
        ]

    def _get_paper_links(self) -> List[str]:
        """获取论文链接"""
        volume_links = self._get_volume_links_from_current_page(self.base_url)
        if len(volume_links) == 0:
            return []
        paper_links = []
        for volume_link in volume_links:
            paper_links.extend(self._get_paper_links_from_current_page(volume_link))
        return paper_links

    def _get_volume_links_from_current_page(self, url: str) -> List[str]:
        """获取卷链接，根据当前页面"""
        logging.info(f"🔍 正在扫描 URL: {url} 的卷")

        if not self.driver_manager.safe_get(url):
            logging.error(f"无法访问卷页面: {url}")
            return []
        
        li_elements = self.driver_manager.driver.find_elements(
            By.CSS_SELECTOR, 'li'
        )
        volume_links = []
        for li in li_elements:
            try:
                a_tag = li.find_element(By.TAG_NAME, 'a')
                href = a_tag.get_attribute('href')
                text = a_tag.text.strip()
                # 解析 Volume 和年份
                # 例如 text = "Volume 22: 2025"
                if "volume" in text.lower():
                    print("原始 Volume 文本:", text)
                    volume_year = text.replace("Volume", "").replace(",", ":").strip()
                    volume, year = volume_year.split(":")
                    if str(self.target_year) in str(year):
                        logging.info(f"✅ 从 URL: {url} 获取到 卷: {volume} 年份: {year} 的链接: {href}")
                        volume_links.append(href)
            except Exception:
                continue

        if len(volume_links) == 0:
            logging.error(f"无法从 URL: {url} 获取到卷")
            return []
        return volume_links

if __name__ == '__main__':
    crawler = DBLPCrawlerJournals(target_year='2022', target_source='tocs')
    output = crawler._get_paper_links()
    print(output)
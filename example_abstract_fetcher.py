#!/usr/bin/env python3
"""
异步摘要获取器使用示例

这个文件展示了如何使用新的 AbstractFetcher 类来并发获取论文摘要
"""

import os
import sys
import asyncio
import logging

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import AbstractFetcher


async def main():
    """主函数示例"""
    
    # 1. 设置数据目录
    data_dir = os.path.join(ROOT_DIR, 'data', 'paper', 'conf_a')
    
    # 2. 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 3. 创建摘要获取器 (并发数=10，你可以根据需要调整)
    fetcher = AbstractFetcher(concurrency=10)
    
    # 4. 开始获取摘要
    print("🚀 开始异步并发获取论文摘要...")
    await fetcher.fetch_abstracts_for_directory(data_dir)
    print("✅ 摘要获取完成!")


def simple_usage():
    """简单使用方式"""
    
    # 数据目录
    data_dir = "你的数据目录路径"
    
    # 直接调用同步版本 (内部会自动处理异步)
    from crawler.dblp_unify.crawler_dblp_unify import main_papers_abstract
    
    # 并发数=5 (默认值)
    main_papers_abstract(data_dir, concurrency=5)
    
    # 或者并发数=10 (更快，但可能对服务器压力更大)
    # main_papers_abstract(data_dir, concurrency=10)


if __name__ == "__main__":
    # 运行异步版本
    asyncio.run(main())
    
    # 或者使用简单版本
    # simple_usage()

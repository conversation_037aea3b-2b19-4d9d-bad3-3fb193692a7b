import os
import sys
from tokenize import Special

ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_PATH)

from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin
from selenium.webdriver.common.by import By
from crawler.base import BaseCrawler
import logging

DBLP_BASE_URL = 'https://dblp.uni-trier.de/db/conf' # DBLP 基础 URL

def get_dblp_special_sources(source: str, target_year: str):
    if source == 'nips' and int(target_year) >= 2020:
        return ['nips', 'neurips']
    else:
        return [source, source]

class DBLPCrawler(BaseCrawler):
    """DBLP 爬虫基类"""

    def __init__(self, target_year=None, target_source=None):
        super().__init__(target_year, target_source)
        self.papers_data = []

        # 构建DBLP URL（包括特殊处理）
        source_list = get_dblp_special_sources(target_source, target_year)
        self.base_url = f"{DBLP_BASE_URL}/{source_list[0].lower()}/{source_list[1].lower()}{target_year}.html" # DBLP 基础 URL

        self.no_text = [
            'proceedings',
            'fullHtml',
            'full'
        ]

    def _get_paper_links_from_current_page(self, url: str) -> List[str]:
        """从当前页面获取论文链接（适用于 DBLP 会议/期刊页面）"""

        logging.info(f"🔍 正在扫描 URL: {url} 的论文")

        if not self.driver_manager.safe_get(url):
            logging.error(f"无法访问年份页面: {url}")
            return []
        
        # 找到所有论文条目的 li
        paper_links = []
        li_elements = self.driver_manager.driver.find_elements(
            By.CSS_SELECTOR, 'li.entry'
        )
        for li in li_elements:
            # 找到每个 li 下的 head 区域的 a 标签
            try:
                a_tag = li.find_element(By.CSS_SELECTOR, 'div.head a')
                href = a_tag.get_attribute('href')
                if href:
                    paper_links.append(href)
            except Exception:
                continue
                
        logging.info(f"✅ 从 URL: {url} 获取到 {len(paper_links)} 篇论文")

        return paper_links

    def _crawl_paper_details(self, paper_url: str) -> Optional[Dict]:
        """爬取单篇TKDD论文的详细信息 - TKDD特异性数据提取"""
        try:
            if not self.driver_manager.safe_get(paper_url):
                logging.error(f"无法访问论文页面: {paper_url}")
                return None

            paper_data = {
                'url': paper_url,
                'crawl_time': datetime.now().isoformat()
            }

            # 提取标题
            title_selectors = ['h1.citation__title', 'h1.hlFld-Title', '.citation__title', 'h1']
            for selector in title_selectors:
                title = self.driver_manager.extract_text_safe(selector)
                if title:
                    paper_data['title'] = title
                    break

            # 提取作者
            author_selector = 'span[property="author"]'
            authors = self.driver_manager.extract_text_safe(author_selector, multiple=True)
            paper_data['authors'] = authors

            # 提取摘要
            abs_selector = '#abstract'
            abstract = self.driver_manager.extract_text_safe(abs_selector)
            cleaned_abstract = self._clean_abstract_text(abstract)
            paper_data['abstract'] = cleaned_abstract
            logging.info(f"获取到摘要 ({len(cleaned_abstract)} 字符)")

            # 提取DOI - 从TKDD URL中提取
            import re
            doi_match = re.search(r'/doi/(.+)', paper_url)
            if doi_match:
                paper_data['doi'] = doi_match.group(1)

            # 提取发表时间
            pub_selector = '.core-date-published'
            pub_date = self.driver_manager.extract_text_safe(pub_selector)
            paper_data['publication_date'] = pub_date

            # 提取PDF链接
            pdf_selector = 'a[title*="View PDF"]'
            pdf_url = self.driver_manager.extract_attribute_safe(pdf_selector, 'href')
            paper_data['pdf_url'] = pdf_url

            return paper_data if paper_data.get('title') else None

        except Exception as e:
            logging.error(f"爬取论文详情失败 ({paper_url}): {e}")
            return None

if __name__ == '__main__':
    crawler = DBLPCrawler(target_year='2024', target_source='aaai')
    output = crawler._get_paper_links(max_papers=10000)
    print(output)
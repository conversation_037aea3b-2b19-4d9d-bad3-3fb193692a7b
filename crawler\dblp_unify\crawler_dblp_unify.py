#!/usr/bin/env python3
"""
DBLP API 论文获取模块
通过DBLP API获取指定会议或期刊的论文信息
"""

import os
import re
import sys
import json
import requests
import logging
import time
import asyncio
import aiohttp

from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path
from tqdm import tqdm

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_DIR)

# 导入CCF A类会议规则
from crawler.dblp_unify.special_rules import get_special_rules
from crawler.dblp_unify.venue import (
    get_venue_name,
    get_classification_by_venue,
    get_ccf_by_venue,
    get_all_venue_by_rule,
)


class DBLPPaperFetcher:
    """DBLP论文获取器"""

    def __init__(self, data_dir: str):
        self.base_url = 'https://dblp.org/search/publ/api'
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DBLP-Paper-Fetcher/1.0'
        })

        # 创建保存目录
        self.data_dir = data_dir
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)

    def _send_request(self, query: str, max_hits: int = 1000, start_from: int = 0) -> Optional[Dict]:
        """
        发送DBLP API请求，带有重试机制

        Args:
            query: 查询字符串
            max_hits: 最大返回结果数
            start_from: 起始位置

        Returns:
            API响应的JSON数据
        """
        params = {
            'q': query,
            'format': 'json',
            'h': min(max_hits, 1000),  # DBLP API限制最大1000
            'f': start_from,
            'c': 0  # 不需要自动补全
        }

        # 重试配置：延迟时间（秒）
        retry_delays = [10, 30, 60, 120, 300]  # 10s, 30s, 1min, 2min, 5min
        max_retries = len(retry_delays)

        for attempt in range(max_retries + 1):  # +1 是因为第一次不算重试
            try:
                response = self.session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                if attempt < max_retries:
                    delay = retry_delays[attempt]
                    logging.warning(f"⚠️ DBLP API请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                    logging.info(f"🔄 等待 {delay} 秒后重试...")
                    time.sleep(delay)
                else:
                    logging.error(f"❌ DBLP API请求最终失败，已重试 {max_retries} 次: {e}")
                    return None

    def _extract_paper_info(self, hit: Dict) -> Dict:
        """
        从DBLP API响应中提取论文信息

        Args:
            hit: DBLP API返回的单篇论文数据

        Returns:
            标准化的论文信息字典
        """
        info = hit.get('info', {})

        # 添加DBLP特有的字段
        paper_info = info.copy()
        paper_info['key'] = hit.get('@id', '')  # DBLP key
        paper_info['dblp_url'] = f"https://dblp.org/rec/{hit.get('@id', '')}" if hit.get('@id') else ''

        # 确保ee字段是列表格式
        if 'ee' in paper_info and isinstance(paper_info['ee'], str):
            paper_info['ee'] = [paper_info['ee']]

        return paper_info

    def _get_all_papers_by_page(self, query: str) -> List[Dict]:
        """
        分页获取所有论文数据

        Args:
            query: 查询字符串

        Returns:
            所有论文信息列表
        """
        all_papers = []
        start_from = 0
        page_size = 1000

        while True:
            result = self._send_request(query, max_hits=page_size, start_from=start_from)

            if not result or 'result' not in result:
                break

            hits_data = result['result'].get('hits', {})
            total = int(hits_data.get('@total', 0))
            hits = hits_data.get('hit', [])
            
            if not hits:
                break

            # 处理当前页的论文
            for hit in hits:
                paper_info = self._extract_paper_info(hit)
                all_papers.append(paper_info)

            # 检查是否还有更多数据
            if len(all_papers) >= total or len(hits) < page_size:
                break

            start_from += page_size
            time.sleep(0.5)  # 避免请求过快

        return all_papers

    def _replace_special_characters(self, text: any) -> str:
        if not text: return ''
        text = str(text)
        # 使用正则表达式替换所有非字母字符为空格
        text = re.sub(r'[^a-zA-Z\s]', ' ', text)
        # 将多个连续空格替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空格
        text = text.strip()
        # 转换为小写
        text = text.lower()

        return text

    def fetch_papers(self, venue_name: str, year: int) -> List[Dict]:
        """
        获取指定会议/期刊和年份的所有论文

        Args:
            venue_name: 会议或期刊名称 (如 'tocs', 'sigmod', 'vldb')
            year: 年份

        Returns:
            论文信息列表
        """
        # 1. 获取正确的venue名称
        dblp_venue_name = get_venue_name(venue_name, year)
        query = f"venue:{dblp_venue_name} year:{year}"

        # 2. 获取论文
        papers = self._get_all_papers_by_page(query)

        # 3. 过滤论文，只保留精确匹配的venue（DBLP API查询和返回结果的venue名称格式可能不一致）
        filtered_papers = []

        # 确定要匹配的venue名称列表
        target_venues = [self._replace_special_characters(dblp_venue_name)] # 原始 venue 名称
        special_rules = get_special_rules(venue_name) # 特殊规则（有些名称多）
        if 'filter_venues' in special_rules:
            target_venues.extend([self._replace_special_characters(venue) for venue in special_rules['filter_venues']])

        for paper in papers:
            paper_venue = paper.get('venue', '')
            # 将 paper_venue 转换为一个字符串列表
            if isinstance(paper_venue, list):
                paper_venue = [self._replace_special_characters(venue) for venue in paper_venue]
            else:
                paper_venue = [self._replace_special_characters(paper_venue)]

            # 检查是否匹配任何目标venue名称（查看是否有交集）
            if set(paper_venue).intersection(target_venues):
                filtered_papers.append(paper)

        # 4. 过滤前信息统计
        type_counts_before = {}
        venue_counts_before = {}
        for p in papers:
            # 4.1 类型分布
            paper_type = p.get('type', 'unknown')
            type_counts_before[paper_type] = type_counts_before.get(paper_type, 0) + 1

            # 4.2 来源分布
            paper_venue = p.get('venue', 'unknown')
            if isinstance(paper_venue, list):
                paper_venue = ', '.join(paper_venue) if paper_venue else 'unknown'
            elif not isinstance(paper_venue, str):
                paper_venue = str(paper_venue) if paper_venue else 'unknown'
            venue_counts_before[paper_venue] = venue_counts_before.get(paper_venue, 0) + 1

        logging.info(f"✅ {dblp_venue_name}'{year}: 过滤前 {len(papers)} -> 过滤后 {len(filtered_papers)}")

        # 5. 过滤后信息统计
        type_counts = {}
        venue_counts = {}
        for p in filtered_papers:
            # 5.1 类型分布
            paper_type = p.get('type', 'unknown')
            type_counts[paper_type] = type_counts.get(paper_type, 0) + 1

            # 5.2 来源分布
            paper_venue = p.get('venue', 'unknown')
            if isinstance(paper_venue, list):
                paper_venue = ', '.join(paper_venue) if paper_venue else 'unknown'
            elif not isinstance(paper_venue, str):
                paper_venue = str(paper_venue) if paper_venue else 'unknown'
            venue_counts[paper_venue] = venue_counts.get(paper_venue, 0) + 1

        return filtered_papers, type_counts, venue_counts, type_counts_before, venue_counts_before

    def save_papers_to_json(self, papers: List[Dict], venue_name: str, year: int,
                           type_counts: Dict, venue_counts: Dict,
                           type_counts_before: Dict, venue_counts_before: Dict,
                           total_papers_before: int) -> str:
        """
        保存论文数据到JSON文件
        Args:
            papers: 过滤后的论文数据列表
            venue_name: 会议/期刊名称
            year: 年份
            type_counts: 过滤后论文类型分布统计
            venue_counts: 过滤后论文来源分布统计
            type_counts_before: 过滤前论文类型分布统计
            venue_counts_before: 过滤前论文来源分布统计
            total_papers_before: 过滤前论文总数
        Returns:
            保存的文件路径
        """
        # 1. 清理文件名中的特殊字符
        clean_venue_name = venue_name.replace(' ', '_').replace('.', '').replace('/', '_')
        filename = f"{clean_venue_name}_{year}.json"
        filepath = os.path.join(self.data_dir, filename)

        # 2. 添加元数据和统计信息
        data_to_save = {
            'metadata': {
                'venue_name': venue_name,
                'year': year,
                'total_papers': len(papers),
                'fetch_time': datetime.now().isoformat(),
                'source': 'DBLP API',
                'type_distribution': type_counts,
                'venue_distribution': venue_counts
            },
            'metadata_before_filtered': {
                'total_papers': total_papers_before,
                'type_distribution': type_counts_before,
                'venue_distribution': venue_counts_before
            },
            'papers': papers
        }

        # 3. 保存到JSON文件
        if total_papers_before == 0:
            return None
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)

        return filepath

    def get_papers_by_venue_and_year(self, venue_name: str, year: int) -> str:
        """
        根据会议/期刊名称和年份获取论文，并保存为JSON格式

        Args:
            venue_name: 会议或期刊名称
            year: 年份

        Returns:
            保存的文件路径
        """
        papers, type_counts, venue_counts, type_counts_before, venue_counts_before = self.fetch_papers(venue_name, year)
        total_papers_before = sum(type_counts_before.values())
        return self.save_papers_to_json(papers, venue_name, year, type_counts, venue_counts,
                                       type_counts_before, venue_counts_before, total_papers_before)

class DBLPPaperInfo:
    """用于对论文数据进行统计和信息的展示"""
    def __init__(self):
        pass

    def info_by_dir(self, dir_path: str):
        """
        从指定目录读取所有JSON文件，展示论文信息
        Args:
            dir_path: 包含JSON文件的目录路径
        """
        from prettytable import PrettyTable
        from pathlib import Path

        if not os.path.exists(dir_path):
            print(f"❌ 目录不存在: {dir_path}")
            return

        # 获取所有JSON文件
        json_files = list(Path(dir_path).glob("*.json"))
        if not json_files:
            print(f"❌ 目录中没有找到JSON文件: {dir_path}")
            return

        print(f"📁 正在分析目录: {dir_path}")
        print(f"📄 找到 {len(json_files)} 个JSON文件")
        print("=" * 80)

        # 收集数据：会议 -> 年份 -> 论文数
        venue_year_data = {}
        all_years = set()

        for json_file in sorted(json_files):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                metadata = data.get('metadata', {})
                venue_name = metadata.get('venue_name', 'N/A')
                year = metadata.get('year', 'N/A')
                total_papers = metadata.get('total_papers', 0)

                if venue_name not in venue_year_data:
                    venue_year_data[venue_name] = {}

                venue_year_data[venue_name][year] = total_papers
                all_years.add(year)

            except Exception as e:
                print(f"⚠️ 读取文件失败: {json_file.name} - {e}")

        # 排序年份
        sorted_years = sorted([y for y in all_years if y != 'N/A'])
        if 'N/A' in all_years:
            sorted_years.append('N/A')

        # 创建表格
        table = PrettyTable()
        table.field_names = ['会议'] + [str(year) for year in sorted_years]

        # 添加数据行
        for venue in sorted(venue_year_data.keys()):
            row = [venue]
            for year in sorted_years:
                count = venue_year_data[venue].get(year, 0)
                row.append(count if count > 0 else '-')
            table.add_row(row)

        # 设置表格样式
        table.align = 'r'  # 右对齐
        table.align['会议'] = 'l'  # 会议名左对齐

        print("\n📊 会议-年份论文数量统计表:")
        print(table)

        # 简单统计
        total_papers = sum(sum(years.values()) for years in venue_year_data.values())
        total_venues = len(venue_year_data)
        print(f"\n📈 汇总: {total_venues} 个会议, 共 {total_papers:,} 篇论文")


class AbstractFetcher:
    """异步并发获取论文摘要的简单类"""

    def __init__(self, concurrency: int = 5):
        """
        初始化摘要获取器

        Args:
            concurrency: 并发数，默认5个
        """
        self.concurrency = concurrency
        self.base_url = "https://api.crossref.org/works/"

        # 统计信息
        self.total_papers = 0
        self.papers_with_abstract = 0
        self.papers_without_doi = 0
        self.papers_abstract_fetched = 0
        self.papers_abstract_failed = 0

    async def fetch_one_abstract(self, session: aiohttp.ClientSession, doi: str) -> Optional[str]:
        """
        获取单个论文的摘要

        Args:
            session: aiohttp会话
            doi: 论文DOI

        Returns:
            摘要文本或None
        """
        try:
            url = f"{self.base_url}{doi}"
            async with session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    abstract = data.get('message', {}).get('abstract', '')

                    if abstract:
                        # 清理HTML标签
                        abstract = re.sub(r'<[^>]+>', '', abstract)
                        abstract = re.sub(r'\s+', ' ', abstract).strip()
                        return abstract

        except Exception as e:
            logging.warning(f"获取摘要失败 {doi}: {e}")

        return None

    async def process_papers_batch(self, papers_batch: List[Dict]) -> List[Dict]:
        """
        批量处理论文，获取摘要

        Args:
            papers_batch: 论文列表

        Returns:
            更新后的论文列表
        """
        # 创建aiohttp会话
        connector = aiohttp.TCPConnector(limit=self.concurrency)
        timeout = aiohttp.ClientTimeout(total=30)
        headers = {'User-Agent': 'DBLP-Abstract-Fetcher/1.0'}

        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        ) as session:

            # 创建任务列表
            tasks = []
            papers_need_abstract = []

            for paper in papers_batch:
                self.total_papers += 1

                # 检查是否已有摘要
                if 'abstract' in paper and paper['abstract']:
                    self.papers_with_abstract += 1
                    continue

                # 检查是否有DOI
                doi = paper.get('doi', '')
                if not doi:
                    self.papers_without_doi += 1
                    continue

                # 需要获取摘要的论文
                papers_need_abstract.append(paper)
                task = self.fetch_one_abstract(session, doi)
                tasks.append(task)

            # 并发执行所有任务
            if tasks:
                abstracts = await asyncio.gather(*tasks, return_exceptions=True)

                # 将结果写回论文数据
                for paper, abstract in zip(papers_need_abstract, abstracts):
                    if isinstance(abstract, str) and abstract:
                        paper['abstract'] = abstract
                        self.papers_abstract_fetched += 1
                    else:
                        self.papers_abstract_failed += 1

        return papers_batch

    async def fetch_abstracts_for_file(self, json_file_path: str) -> bool:
        """
        为单个JSON文件中的所有论文获取摘要

        Args:
            json_file_path: JSON文件路径

        Returns:
            是否有更新
        """
        try:
            # 读取文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            papers = data.get('papers', [])
            if not papers:
                return False

            # 处理论文
            updated_papers = await self.process_papers_batch(papers)

            # 检查是否有更新
            original_abstracts = sum(1 for p in papers if 'abstract' in p and p['abstract'])
            updated_abstracts = sum(1 for p in updated_papers if 'abstract' in p and p['abstract'])

            if updated_abstracts > original_abstracts:
                # 保存更新后的数据
                data['papers'] = updated_papers
                with open(json_file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True

        except Exception as e:
            logging.error(f"处理文件失败 {json_file_path}: {e}")

        return False

    async def fetch_abstracts_for_directory(self, data_dir: str):
        """
        为目录中所有JSON文件获取摘要

        Args:
            data_dir: 数据目录路径
        """
        # 获取所有JSON文件
        json_files = list(Path(data_dir).glob("*.json"))

        if not json_files:
            logging.error(f"❌ 目录中没有找到JSON文件: {data_dir}")
            return

        logging.info(f"📁 开始处理目录: {data_dir}, 文件数: {len(json_files)}")

        # 处理每个文件
        for json_file in tqdm(json_files, desc="处理文件", unit="个"):
            await self.fetch_abstracts_for_file(str(json_file))

        # 输出统计信息
        self.print_statistics()

    def print_statistics(self):
        """打印统计信息"""
        logging.info("=" * 80)
        logging.info("📊 摘要获取统计:")
        logging.info(f"📄 总论文数: {self.total_papers:,}")
        logging.info(f"✅ 已有摘要: {self.papers_with_abstract:,}")
        logging.info(f"❌ 无DOI信息: {self.papers_without_doi:,}")
        logging.info(f"🆕 新获取摘要: {self.papers_abstract_fetched:,}")
        logging.info(f"⚠️ 获取失败: {self.papers_abstract_failed:,}")
        logging.info("=" * 80)



def main_papers_meta(data_dir: str):

    # 配置需要处理的会议
    # venues = ['iclr']
    venues = get_all_venue_by_rule('a', 'conf')
    # 没问题的会议
    SKIP_CONFS = [
        'sc', # 搜索结果过多
        'fse_esec', # 合并拆分内容过多，不统一
        'pldi', # 混到了其他期刊中
        'popl', # 和 pldi 混合了
        'ooplsa', # 检索为空
        'vr', # x 检索即失败
        'vis', # x 数量对不上，应该 120 结果 53 篇
        'cscw', # 搜索数量极少，不对劲，正常接收了 2235 篇
        'ubicomp', # 搜索数量极少，不对劲，正常接收了 764 篇
    ]

    # 3. 开始获取
    fetcher = DBLPPaperFetcher(data_dir=data_dir)
    years = [i for i in range(2021, 2022)]
    for year in years:
        for venue in venues:
            if venue in SKIP_CONFS: continue
            filepath = fetcher.get_papers_by_venue_and_year(venue, year)
            time.sleep(0.5)

def main_infos(data_dir: str):
    info = DBLPPaperInfo()
    info.info_by_dir(data_dir)

async def main_papers_abstract_async(data_dir: str, concurrency: int = 5):
    """
    异步并发获取论文摘要 - 新版本

    Args:
        data_dir: 包含JSON文件的目录路径
        concurrency: 并发数，默认5个
    """
    fetcher = AbstractFetcher(concurrency=concurrency)
    await fetcher.fetch_abstracts_for_directory(data_dir)


def main_papers_abstract(data_dir: str, concurrency: int = 5):
    """
    获取论文摘要的入口函数

    Args:
        data_dir: 包含JSON文件的目录路径
        concurrency: 并发数，默认5个
    """
    # 运行异步函数
    asyncio.run(main_papers_abstract_async(data_dir, concurrency))



if __name__ == "__main__":
    # 1. 定义保存目录
    data_dir = os.path.join(ROOT_DIR, 'data', 'paper', 'conf_a')
    log_dir = os.path.join(ROOT_DIR, 'data', 'logs')

    # 2. 配置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(log_dir, f'log_{int(time.time())}.txt'), mode='w', encoding='utf-8')
        ]
    )
    
    # 3. 获取论文元信息
    main_papers_meta(data_dir)

    # 4. 查看获取后的信息统计
    # main_infos(data_dir)

    # 5. 获取论文摘要 (异步并发版本，并发数=5)
    # main_papers_abstract(data_dir=data_dir, concurrency=5)
    

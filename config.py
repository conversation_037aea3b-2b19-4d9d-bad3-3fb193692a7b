from crawler.acm.crawler_acm_loi import AC<PERSON>rawlerLoi
from crawler.acm.crawler_acm_conf import ACMCrawlerConf

from crawler.dblp.crawler_dblp_journals import DBLPCrawlerJournals
from crawler.dblp.crawler_dblp_conf import DBLPCrawlerConf

# 期刊和会议配置
PUBLICATIONS = {


    # ===================== 一、计算机体系结构/并行与分布计算/存储系统 =====================

    # 期刊  
    ## CCF A 类
    'tocs': DBLPCrawlerJournals,
    'tos': DBLPCrawlerJournals,
    'tcad': DBLPCrawlerJournals,
    'tc': DBLPCrawlerJournals,
    'tpds': DBLPCrawlerJournals,
    'taco': DBLPCrawlerJournals,
    ## CCF B 类

    # 会议
    ## CCF A 类
    'ppopp': DBLPCrawlerConf,
    'fast': DBLPCrawlerConf,
    'dac': DBLPCrawlerConf,
    'hpca': DBLPCrawlerConf,
    'micro': DBLPCrawlerConf,
    'sc': DBLPCrawlerConf,
    'asplos': DBLPCrawlerConf, # index 后添加 -x
    'isca': DBLPCrawlerConf,
    'usenix': DBLPCrawlerConf,

    
    # ===================== 二、计算机网络 =====================

    # 期刊
    ## CCF A 类
    'jsac': DBLPCrawlerJournals,    
    'tmc': DBLPCrawlerJournals,
    'ton': DBLPCrawlerJournals,

    # 会议
    ## CCF A 类
    'sigcomm': DBLPCrawlerConf,
    'mobicom': DBLPCrawlerConf,
    'infocom': DBLPCrawlerConf,
    'nsdi': DBLPCrawlerConf,

    # ===================== 三、网络与信息安全 =====================

    # 期刊
    ## CCF A 类
    'tdsc': DBLPCrawlerJournals,
    'tifs': DBLPCrawlerJournals,
    'joc': DBLPCrawlerJournals,

    # 会议
    ## CCF A 类
    'ccs': DBLPCrawlerConf,
    'eurocrypt': DBLPCrawlerConf,
    'sp': DBLPCrawlerConf,
    'crypto': DBLPCrawlerConf,
    'uss': DBLPCrawlerConf,
    'ndss': DBLPCrawlerConf,


    # ===================== 四、软件工程/系统软件/程序设计语言 =====================

    # 期刊
    ## CCF A 类
    'toplas': DBLPCrawlerJournals,
    'tosem': DBLPCrawlerJournals,
    'tse': DBLPCrawlerJournals,
    'tsc': DBLPCrawlerJournals,

    # 会议
    ## CCF A 类
    'pldi': DBLPCrawlerConf,
    'popl': DBLPCrawlerConf,
    'sigsoft': DBLPCrawlerConf,
    'sosp': DBLPCrawlerConf,
    'ooplsa': DBLPCrawlerConf,
    'ase': DBLPCrawlerConf,
    'icse': DBLPCrawlerConf,
    'issta': DBLPCrawlerConf,
    'osdi': DBLPCrawlerConf,
    'fm': DBLPCrawlerConf,

    # ===================== 五、数据库/数据挖掘/内容检索 =====================

    # 会议
    ## CCF A 类
    'sigmod': DBLPCrawlerConf,
    'kdd': DBLPCrawlerConf,
    'icde': DBLPCrawlerConf,
    'sigir': DBLPCrawlerConf,
    # 'vldb': DBLPCrawlerConf, # vldb 这个链接全是 workshop，因此我们暂时只获取其期刊结果

    # 期刊
    ## CCF A 类
    # 'tods': DBLPCrawlerJournals,
    # 'tois': DBLPCrawlerJournals,
    # 'tkde': DBLPCrawlerJournals,
    'vldb': DBLPCrawlerJournals,

    # ===================== ACM 出版商 =====================
}

def get_crawler(source: str):
    if source not in PUBLICATIONS:
        raise ValueError(f"Unsupported source: {source}")
    return PUBLICATIONS[source]
